{"version": 2, "buildCommand": "cd frontend && npm run build", "outputDirectory": "frontend/dist", "builds": [{"src": "api/index.js", "use": "@vercel/node", "config": {"maxLambdaSize": "50mb"}}], "routes": [{"src": "/api/(.*)", "dest": "/api/index.js"}, {"src": "/(.*)", "dest": "/index.html"}], "env": {"NODE_ENV": "production", "SERVE_FRONTEND": "false"}, "functions": {"api/index.js": {"maxDuration": 30, "memory": 1024}}}