{"version": 2, "builds": [{"src": "frontend/package.json", "use": "@vercel/static-build", "config": {"distDir": "frontend/dist"}}, {"src": "api/index.js", "use": "@vercel/node", "config": {"maxLambdaSize": "50mb"}}], "routes": [{"src": "/api/(.*)", "dest": "/api/index.js"}, {"src": "/(.*)", "dest": "/frontend/dist/index.html"}], "env": {"NODE_ENV": "production", "SERVE_FRONTEND": "false"}, "functions": {"api/index.js": {"maxDuration": 30, "memory": 1024}}}